server:
  port: 9090
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: demo-temporal
  # Redis configuration
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  # temporal specific configs
  temporal:
    connection:
      target: 127.0.0.1:7233
      target.namespace: default
    workersAutoDiscovery:
      packages: com.test.temporal

logging:
  level:
    com.test.temporal: INFO
    io.temporal: WARN
