package com.test.temporal.workflow;

import static com.test.temporal.Constants.TASK_QUEUE;

import com.test.temporal.activity.ExternalDataActivities;
import com.test.temporal.activity.PersistenceActivities;
import com.test.temporal.model.AnalysisConfig;
import com.test.temporal.model.EntityData;
import com.test.temporal.model.NewEntity;
import io.temporal.activity.ActivityOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@WorkflowImpl(taskQueues = TASK_QUEUE)
public class EntityExplorationWorkflowImpl implements EntityExplorationWorkflow {

    private final ExternalDataActivities externalActivities =
            Workflow.newActivityStub(ExternalDataActivities.class,
					ActivityOptions.newBuilder().setStartToCloseTimeout(Duration.ofSeconds(20)).build());
    private final PersistenceActivities persistenceActivities =
            Workflow.newActivityStub(PersistenceActivities.class,
					ActivityOptions.newBuilder().setStartToCloseTimeout(Duration.ofSeconds(20)).build());

    @Override
    public void explore(String entityId, String entityType, int currentDepth, AnalysisConfig config) {
        // Folosim o singură adâncime comună pentru toate entitățile
        if (currentDepth >= config.getMaxDepth()) {
            return;
        }

        List<EntityData> allExtractedData = new ArrayList<>();

        switch (entityType) {
            case "CNP-P":
            case "CNP-R":
                // Pas 1: Apelăm activitățile independente în paralel
                Promise<EntityData> personBasePromise = Async.function(externalActivities::fetchPersonBaseData, entityId);
                Promise<EntityData> revisalPromise = Async.function(externalActivities::fetchRevisalData, entityId);
                Promise<EntityData> patrimvenPromise = Async.function(externalActivities::fetchPatrimvenData, entityId);

                // Așteptăm datele de bază ale persoanei pentru a continua
                EntityData personBaseData = personBasePromise.get();
                if (personBaseData != null) allExtractedData.add(personBaseData);

                // Pas 2: Acum că avem datele de bază, cerem rudele (apel secvențial)
                EntityData relativesData = externalActivities.fetchPersonRelatives(entityId);
                if (relativesData != null) allExtractedData.add(relativesData);

                // Așteptăm și finalizarea celorlalte apeluri paralele
                EntityData revisalData = revisalPromise.get();
                if (revisalData != null) allExtractedData.add(revisalData);
                EntityData patrimvenData = patrimvenPromise.get();
                if (patrimvenData != null) allExtractedData.add(patrimvenData);
                break;

            case "CUI":
                // Flux secvențial pentru companii
                EntityData companyBaseData = externalActivities.fetchCompanyBaseData(entityId);
                if (companyBaseData != null) allExtractedData.add(companyBaseData);

                EntityData affiliatedData = externalActivities.fetchAffiliatedCompanies(entityId);
                if (affiliatedData != null) allExtractedData.add(affiliatedData);
                break;
        }

        // Persistăm toate datele colectate
        for (EntityData data : allExtractedData) {
            persistenceActivities.persistData(entityId, data);
        }

        // Colectăm entitățile noi descoperite
        List<NewEntity> allDiscoveredEntities = allExtractedData.stream()
                .flatMap(data -> data.getDiscoveredEntities().stream())
                .collect(Collectors.toList());

        // Lansăm copii pentru noile entități
        List<Promise<Void>> grandChildPromises = new ArrayList<>();
		String parentWorkflowId = Workflow.getInfo().getParentWorkflowId().get();
		FiAnalysisWorkflow parent = Workflow.newExternalWorkflowStub(FiAnalysisWorkflow.class, parentWorkflowId);

        for (NewEntity newEntity : allDiscoveredEntities) {
//            if (!parent.isEntityProcessed(newEntity.getId())) {
                parent.markAsProcessed(newEntity.getId());
                EntityExplorationWorkflow grandChild = Workflow.newChildWorkflowStub(EntityExplorationWorkflow.class);
                grandChildPromises.add(Async.procedure(
                        grandChild::explore,
                        newEntity.getId(),
                        newEntity.getType(),
                        currentDepth + 1,
                        config
                ));
//            }
        }
        Promise.allOf(grandChildPromises).get();
    }
}
