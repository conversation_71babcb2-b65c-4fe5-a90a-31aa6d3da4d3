package com.test.temporal.workflow;

import static com.test.temporal.Constants.TASK_QUEUE;

import com.test.temporal.activity.ExtractionActivities;
import com.test.temporal.model.AnalysisConfig;
import com.test.temporal.model.InitialEntities;
import io.temporal.activity.ActivityOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@WorkflowImpl(taskQueues = TASK_QUEUE)
public class FiAnalysisWorkflowImpl implements FiAnalysisWorkflow {

    private final Set<String> processedEntities = new HashSet<>();
    private final ExtractionActivities extractionActivities =
            Workflow.newActivityStub(ExtractionActivities.class,
					ActivityOptions.newBuilder().setStartToCloseTimeout(Duration.ofSeconds(10)).build());

    @Override
    public String startAnalysis(String fiId) {
        AnalysisConfig config = extractionActivities.getAnalysisConfig();
        InitialEntities initialEntities = extractionActivities.extractInitialEntities(fiId);

        processedEntities.addAll(initialEntities.getCnps());
        processedEntities.addAll(initialEntities.getCuis());

        List<Promise<Void>> childPromises = new ArrayList<>();
        for (String cnp : initialEntities.getCnps()) {
            EntityExplorationWorkflow child = Workflow.newChildWorkflowStub(EntityExplorationWorkflow.class);
            childPromises.add(Async.procedure(child::explore, cnp, "CNP-P", 0, config));
        }
        for (String cui : initialEntities.getCuis()) {
            EntityExplorationWorkflow child = Workflow.newChildWorkflowStub(EntityExplorationWorkflow.class);
            childPromises.add(Async.procedure(child::explore, cui, "CUI", 0, config));
        }

        Promise.allOf(childPromises).get();
        // algo detectie conflict interese
        // ...

		return "Analysis completed for FI ID: " + fiId;
    }

    @Override
    public void markAsProcessed(String entityId) {
        this.processedEntities.add(entityId);
    }

}
