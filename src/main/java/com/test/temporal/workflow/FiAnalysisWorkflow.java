package com.test.temporal.workflow;

import io.temporal.workflow.QueryMethod;
import io.temporal.workflow.SignalMethod;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;

@WorkflowInterface
public interface FiAnalysisWorkflow {

    @WorkflowMethod
    void startAnalysis(String fiId);

    @SignalMethod
    void markAsProcessed(String entityId);

    @QueryMethod
    boolean isEntityProcessed(String entityId);
}
