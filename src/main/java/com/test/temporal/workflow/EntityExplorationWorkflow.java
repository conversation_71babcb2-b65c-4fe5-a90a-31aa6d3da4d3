package com.test.temporal.workflow;

import com.test.temporal.model.AnalysisConfig;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;

/**
 * Workflow-ul Copil (Explorator) pentru explorarea unei entități specifice.
 */
@WorkflowInterface
public interface EntityExplorationWorkflow {
    
    @WorkflowMethod
    void explore(String entityId, String entityType, int currentDepth, AnalysisConfig config, String fiId);
}
