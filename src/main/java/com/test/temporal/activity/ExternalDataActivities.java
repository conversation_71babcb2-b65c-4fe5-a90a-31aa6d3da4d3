package com.test.temporal.activity;

import com.test.temporal.model.EntityData;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

/**
 * Activități granulare pentru fiecare interacțiune cu un serviciu extern.
 */
@ActivityInterface
public interface ExternalDataActivities {
    
    // --- Pentru PERSOANE ---
    @ActivityMethod
    EntityData fetchPersonBaseData(String cnp);
    
    @ActivityMethod
    EntityData fetchPersonRelatives(String cnp);

    @ActivityMethod
    EntityData fetchRevisalData(String cnp);
    
    @ActivityMethod
    EntityData fetchPatrimvenData(String cnp);

    // --- Pentru COMPANII ---
    @ActivityMethod
    EntityData fetchCompanyBaseData(String cui);

    @ActivityMethod
    EntityData fetchAffiliatedCompanies(String cui);
}
