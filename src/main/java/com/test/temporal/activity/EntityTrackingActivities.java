package com.test.temporal.activity;

import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

/**
 * Activities for entity tracking using Redis cache.
 * These activities provide distributed entity deduplication across workflow instances.
 */
@ActivityInterface
public interface EntityTrackingActivities {

    /**
     * Checks if an entity has already been processed for a given FI ID.
     * 
     * @param fiId The FI (Formular Integritate) ID
     * @param entityId The entity ID (CNP or CUI)
     * @return true if entity has been processed, false otherwise
     */
    @ActivityMethod
    boolean isEntityProcessed(String fiId, String entityId);

    /**
     * Attempts to claim processing rights for an entity.
     * This is an atomic operation that both checks and marks the entity.
     * 
     * @param fiId The FI (Formular Integritate) ID
     * @param entityId The entity ID (CNP or CUI)
     * @return true if processing rights were claimed (entity wasn't processed), false if already processed
     */
    @ActivityMethod
    boolean claimEntityProcessing(String fiId, String entityId);

    /**
     * Removes the processing mark for an entity (for testing or error recovery).
     * 
     * @param fiId The FI (Formular Integritate) ID
     * @param entityId The entity ID (CNP or CUI)
     * @return true if the key was removed, false if it didn't exist
     */
    @ActivityMethod
    boolean removeEntityProcessing(String fiId, String entityId);
}
