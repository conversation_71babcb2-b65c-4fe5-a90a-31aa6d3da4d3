package com.test.temporal.activity.impl;

import static com.test.temporal.Constants.TASK_QUEUE;

import com.test.temporal.activity.PersistenceActivities;
import com.test.temporal.model.EntityData;
import io.temporal.spring.boot.ActivityImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
@ActivityImpl(taskQueues = TASK_QUEUE)
public class PersistenceActivitiesImpl implements PersistenceActivities {

    private static final Logger logger = LoggerFactory.getLogger(PersistenceActivitiesImpl.class);

    @Override
    public void persistData(String parentEntityId, EntityData data) {
        // Simulăm persistența datelor în baza de date
        logger.info("Persisting data for entity {} from source {}: {}", 
                   parentEntityId, data.getSource(), data.getProperties());
        
        // În implementarea reală, aici ar fi logica de salvare în baza de date
        // De exemplu: repository.save(new EntityRecord(parentEntityId, data));
    }

}
