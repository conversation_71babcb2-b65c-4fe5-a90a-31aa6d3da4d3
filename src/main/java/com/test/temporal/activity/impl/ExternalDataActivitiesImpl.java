package com.test.temporal.activity.impl;

import com.test.temporal.activity.ExternalDataActivities;
import com.test.temporal.model.EntityData;
import com.test.temporal.model.NewEntity;
import io.temporal.spring.boot.ActivityImpl;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Component
@ActivityImpl
public class ExternalDataActivitiesImpl implements ExternalDataActivities {

    @Override
    public EntityData fetchPersonBaseData(String cnp) {
        EntityData data = new EntityData("DEPABD");
        Map<String, Object> properties = new HashMap<>();
        properties.put("nume", "Popescu");
        properties.put("prenume", "Ion");
        properties.put("cnp", cnp);
        data.setProperties(properties);
        
        // Simulăm descoperirea unei rude
        data.addDiscoveredEntity(new NewEntity("1111111111111", "CNP-R"));
        return data;
    }

    @Override
    public EntityData fetchPersonRelatives(String cnp) {
        EntityData data = new EntityData("DEPABD_RELATIVES");
        Map<String, Object> properties = new HashMap<>();
        properties.put("relatives_count", 2);
        data.setProperties(properties);
        
        // Simulăm descoperirea rudelor
        data.setDiscoveredEntities(Arrays.asList(
                new NewEntity("2222222222222", "CNP-R"),
                new NewEntity("3333333333333", "CNP-R")
        ));
        return data;
    }

    @Override
    public EntityData fetchRevisalData(String cnp) {
        EntityData data = new EntityData("REVISAL");
        Map<String, Object> properties = new HashMap<>();
        properties.put("employer", "SC Test SRL");
        properties.put("salary", 5000);
        data.setProperties(properties);
        
        // Simulăm descoperirea unei companii angajatoare
        data.addDiscoveredEntity(new NewEntity("11111111", "CUI"));
        return data;
    }

    @Override
    public EntityData fetchPatrimvenData(String cnp) {
        EntityData data = new EntityData("PATRIMVEN");
        Map<String, Object> properties = new HashMap<>();
        properties.put("properties_count", 1);
        properties.put("total_value", 150000);
        data.setProperties(properties);
        return data;
    }

    @Override
    public EntityData fetchCompanyBaseData(String cui) {
        EntityData data = new EntityData("ONRC");
        Map<String, Object> properties = new HashMap<>();
        properties.put("denumire", "SC Test SRL");
        properties.put("cui", cui);
        properties.put("capital_social", 200);
        data.setProperties(properties);
        
        // Simulăm descoperirea unui administrator
        data.addDiscoveredEntity(new NewEntity("4444444444444", "CNP-P"));
        return data;
    }

    @Override
    public EntityData fetchAffiliatedCompanies(String cui) {
        EntityData data = new EntityData("ONRC_AFFILIATED");
        Map<String, Object> properties = new HashMap<>();
        properties.put("affiliated_count", 1);
        data.setProperties(properties);
        
        // Simulăm descoperirea unei companii afiliate
        data.addDiscoveredEntity(new NewEntity("22222222", "CUI"));
        return data;
    }
}
