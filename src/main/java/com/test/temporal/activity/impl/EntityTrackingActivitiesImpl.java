package com.test.temporal.activity.impl;

import com.test.temporal.activity.EntityTrackingActivities;
import com.test.temporal.service.EntityTrackingService;
import io.temporal.spring.boot.ActivityImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Implementation of entity tracking activities using Redis cache.
 */
@Component
@ActivityImpl
public class EntityTrackingActivitiesImpl implements EntityTrackingActivities {

    private static final Logger logger = LoggerFactory.getLogger(EntityTrackingActivitiesImpl.class);

    @Autowired
    private EntityTrackingService entityTrackingService;

    @Override
    public boolean isEntityProcessed(String fiId, String entityId) {
        logger.debug("Checking if entity {} is processed for FI {}", entityId, fiId);
        return entityTrackingService.isEntityProcessed(fiId, entityId);
    }

    @Override
    public boolean claimEntityProcessing(String fiId, String entityId) {
        logger.debug("Attempting to claim processing for entity {} in FI {}", entityId, fiId);
        boolean claimed = entityTrackingService.claimEntityProcessing(fiId, entityId);
        
        if (claimed) {
            logger.info("Successfully claimed processing rights for entity {} in FI {}", entityId, fiId);
        } else {
            logger.info("Entity {} in FI {} is already being processed, skipping", entityId, fiId);
        }
        
        return claimed;
    }

    @Override
    public boolean removeEntityProcessing(String fiId, String entityId) {
        logger.debug("Removing processing mark for entity {} in FI {}", entityId, fiId);
        return entityTrackingService.removeEntityProcessing(fiId, entityId);
    }
}
