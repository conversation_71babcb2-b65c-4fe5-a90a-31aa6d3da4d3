package com.test.temporal.activity.impl;

import com.test.temporal.activity.ExtractionActivities;
import com.test.temporal.model.AnalysisConfig;
import com.test.temporal.model.InitialEntities;
import io.temporal.spring.boot.ActivityImpl;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
@ActivityImpl
public class ExtractionActivitiesImpl implements ExtractionActivities {

    @Override
    public AnalysisConfig getAnalysisConfig() {
        // Returnăm o configurație cu adâncime comună de 3 niveluri
        return new AnalysisConfig(3);
    }

    @Override
    public InitialEntities extractInitialEntities(String fiId) {
        // Simulăm extragerea entităților inițiale din Formularul de Integritate
        return new InitialEntities(
                Arrays.asList("1234567890123", "9876543210987"), // CNP-uri
                Arrays.asList("12345678", "87654321") // CUI-uri
        );
    }
}
