package com.test.temporal.activity;

import com.test.temporal.model.AnalysisConfig;
import com.test.temporal.model.InitialEntities;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

/**
 * Activități pentru extragerea datelor inițiale și a configurației.
 */
@ActivityInterface
public interface ExtractionActivities {
    
    @ActivityMethod
    AnalysisConfig getAnalysisConfig();

    @ActivityMethod
    InitialEntities extractInitialEntities(String fiId);
}
