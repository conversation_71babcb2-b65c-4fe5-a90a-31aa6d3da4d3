package com.test.temporal.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Un container generic pentru datele returnate de o activitate externă.
 */
@Data
@NoArgsConstructor
public class EntityData {
    private String source; // ex: "ONRC", "DEPABD"
    private Map<String, Object> properties = new HashMap<>();
    private List<NewEntity> discoveredEntities = new ArrayList<>();

    public EntityData(String source) {
        this.source = source;
    }

    public EntityData(String source, Map<String, Object> properties, List<NewEntity> discoveredEntities) {
        this.source = source;
        this.properties = properties != null ? properties : new HashMap<>();
        this.discoveredEntities = discoveredEntities != null ? discoveredEntities : new ArrayList<>();
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties != null ? properties : new HashMap<>();
    }

    public void setDiscoveredEntities(List<NewEntity> discoveredEntities) {
        this.discoveredEntities = discoveredEntities != null ? discoveredEntities : new ArrayList<>();
    }

    public void addDiscoveredEntity(NewEntity entity) {
        if (entity != null) {
            this.discoveredEntities.add(entity);
        }
    }

    public void addProperty(String key, Object value) {
        this.properties.put(key, value);
    }
}
