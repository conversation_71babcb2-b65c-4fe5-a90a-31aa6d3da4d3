package com.test.temporal.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Conține listele de entități inițiale extrase din Formularul de Integritate.
 */
@Data
@NoArgsConstructor
public class InitialEntities {
    private List<String> cnps = new ArrayList<>();
    private List<String> cuis = new ArrayList<>();

    public InitialEntities(List<String> cnps, List<String> cuis) {
        this.cnps = cnps != null ? cnps : new ArrayList<>();
        this.cuis = cuis != null ? cuis : new ArrayList<>();
    }

    public void setCnps(List<String> cnps) {
        this.cnps = cnps != null ? cnps : new ArrayList<>();
    }

    public void setCuis(List<String> cuis) {
        this.cuis = cuis != null ? cuis : new ArrayList<>();
    }
}
