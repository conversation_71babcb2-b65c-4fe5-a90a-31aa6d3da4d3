package com.test.temporal.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * Service for tracking processed entities using Redis cache.
 * Provides distributed entity deduplication across workflow instances.
 */
@Service
public class EntityTrackingService {

    private static final Logger logger = LoggerFactory.getLogger(EntityTrackingService.class);
    
    private static final String KEY_PREFIX = "fi";
    private static final String ENTITY_PREFIX = "entity";
    private static final Duration EXPIRATION_TIME = Duration.ofMinutes(5); // 5 minutes = 300 seconds
    private static final String PROCESSED_VALUE = "processed";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * Checks if an entity has already been processed for a given FI ID.
     * 
     * @param fiId The FI (Formular Integritate) ID
     * @param entityId The entity ID (CNP or CUI)
     * @return true if entity has been processed, false otherwise
     */
    public boolean isEntityProcessed(String fiId, String entityId) {
        String key = buildRedisKey(fiId, entityId);
        Boolean exists = redisTemplate.hasKey(key);
        
        logger.debug("Checking if entity {} is processed for FI {}: {}", entityId, fiId, exists);
        return Boolean.TRUE.equals(exists);
    }

    /**
     * Marks an entity as processed for a given FI ID.
     * Sets the key with expiration time to avoid memory leaks.
     * 
     * @param fiId The FI (Formular Integritate) ID
     * @param entityId The entity ID (CNP or CUI)
     * @return true if entity was successfully marked (wasn't already processed), false if already existed
     */
    public boolean markEntityAsProcessed(String fiId, String entityId) {
        String key = buildRedisKey(fiId, entityId);
        
        // Use SET with NX (only if not exists) and EX (expiration)
        Boolean wasSet = redisTemplate.opsForValue().setIfAbsent(key, PROCESSED_VALUE, EXPIRATION_TIME);
        
        logger.debug("Marking entity {} as processed for FI {}: {}", entityId, fiId, wasSet);
        return Boolean.TRUE.equals(wasSet);
    }

    /**
     * Attempts to claim processing rights for an entity.
     * This is an atomic operation that both checks and marks the entity.
     * 
     * @param fiId The FI (Formular Integritate) ID
     * @param entityId The entity ID (CNP or CUI)
     * @return true if processing rights were claimed (entity wasn't processed), false if already processed
     */
    public boolean claimEntityProcessing(String fiId, String entityId) {
        return markEntityAsProcessed(fiId, entityId);
    }

    /**
     * Removes the processing mark for an entity (for testing or error recovery).
     * 
     * @param fiId The FI (Formular Integritate) ID
     * @param entityId The entity ID (CNP or CUI)
     * @return true if the key was removed, false if it didn't exist
     */
    public boolean removeEntityProcessing(String fiId, String entityId) {
        String key = buildRedisKey(fiId, entityId);
        Boolean deleted = redisTemplate.delete(key);
        
        logger.debug("Removing processing mark for entity {} in FI {}: {}", entityId, fiId, deleted);
        return Boolean.TRUE.equals(deleted);
    }

    /**
     * Builds the Redis key for entity tracking.
     * Format: fi:{fiId}:entity:{entityId}
     * 
     * @param fiId The FI ID
     * @param entityId The entity ID
     * @return The Redis key
     */
    private String buildRedisKey(String fiId, String entityId) {
        return String.format("%s:%s:%s:%s", KEY_PREFIX, fiId, ENTITY_PREFIX, entityId);
    }

    /**
     * Gets the current expiration time setting.
     * 
     * @return The expiration duration
     */
    public Duration getExpirationTime() {
        return EXPIRATION_TIME;
    }
}
