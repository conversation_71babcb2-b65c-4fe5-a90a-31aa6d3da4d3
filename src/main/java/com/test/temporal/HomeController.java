package com.test.temporal;

import static com.test.temporal.Constants.TASK_QUEUE;
import static com.test.temporal.Constants.WORKFLOW_ID;

import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.test.temporal.workflow.FiAnalysisWorkflow;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;

@RestController
public class HomeController {

	@Autowired
	private WorkflowClient client;

	private final AtomicInteger counter = new AtomicInteger(0);

	@GetMapping
	public void trigger() {
		FiAnalysisWorkflow workflow = client.newWorkflowStub(FiAnalysisWorkflow.class,
				WorkflowOptions.newBuilder()
						.setTaskQueue(TASK_QUEUE)
						.setWorkflowId((WORKFLOW_ID).concat(String.valueOf(counter.getAndIncrement())))
						.build()
		);

		workflow.analyze();
	}
}
