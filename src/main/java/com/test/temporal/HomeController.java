package com.test.temporal;

import static com.test.temporal.Constants.TASK_QUEUE;
import static com.test.temporal.Constants.WORKFLOW_ID;

import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.test.temporal.workflow.FiAnalysisWorkflow;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;

@RestController
public class HomeController {

	@Autowired
	private WorkflowClient client;

	private final AtomicInteger counter = new AtomicInteger(0);

	@GetMapping("/")
	public String home() {
		return "Hello World! FI Analysis Service is running on port 9090. Redis-based entity tracking enabled.";
	}

	@GetMapping("/start/{fiId}")
	public String startAnalysis(@PathVariable String fiId) {
		FiAnalysisWorkflow workflow = client.newWorkflowStub(FiAnalysisWorkflow.class,
				WorkflowOptions.newBuilder()
					.setTaskQueue(TASK_QUEUE)
					.setWorkflowId("fi-analysis-" + fiId + "-" + counter.getAndIncrement())
					.build());

		// Start workflow asynchronously
		WorkflowClient.start(workflow::startAnalysis, fiId);

		return "FI Analysis workflow started for FI ID: " + fiId;
	}

	@GetMapping("/start")
	public String startDefault() {
		return startAnalysis("default-fi-123");
	}
}
