package com.test.temporal;

import com.test.temporal.workflow.FiAnalysisWorkflow;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static com.test.temporal.Constants.TASK_QUEUE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@SpringBootTest
@ActiveProfiles("test")
public class FiAnalysisIntegrationTest {

    @Autowired
    private WorkflowClient workflowClient;

    @Test
    public void testFiAnalysisWorkflowExecution() {
        // Create workflow stub
        FiAnalysisWorkflow workflow = workflowClient.newWorkflowStub(
                FiAnalysisWorkflow.class,
                WorkflowOptions.newBuilder()
                        .setTaskQueue(TASK_QUEUE)
                        .setWorkflowId("integration-test-fi-analysis")
                        .build());

        // Test that workflow can be started without errors
        assertDoesNotThrow(() -> {
            WorkflowClient.start(workflow::startAnalysis, "test-fi-123");
        });
    }
}
