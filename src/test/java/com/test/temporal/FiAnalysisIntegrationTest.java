package com.test.temporal;

import com.test.temporal.service.EntityTrackingService;
import com.test.temporal.workflow.FiAnalysisWorkflow;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static com.test.temporal.Constants.TASK_QUEUE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest
@ActiveProfiles("test")
public class FiAnalysisIntegrationTest {

    @Autowired
    private WorkflowClient workflowClient;

    @Autowired
    private EntityTrackingService entityTrackingService;

    @Test
    public void testEntityTrackingServiceIsAvailable() {
        // Verify that Redis-based entity tracking service is properly configured
        assertNotNull(entityTrackingService);
        
        // Test basic functionality
        String testFiId = "integration-test-fi";
        String testEntityId = "test-entity-123";
        
        // Should be able to claim processing
        boolean claimed = entityTrackingService.claimEntityProcessing(testFiId, testEntityId);
        assertNotNull(claimed); // Just verify no exceptions are thrown
        
        // Clean up
        entityTrackingService.removeEntityProcessing(testFiId, testEntityId);
    }

    @Test
    public void testFiAnalysisWorkflowExecution() {
        // Create workflow stub
        FiAnalysisWorkflow workflow = workflowClient.newWorkflowStub(
                FiAnalysisWorkflow.class,
                WorkflowOptions.newBuilder()
                        .setTaskQueue(TASK_QUEUE)
                        .setWorkflowId("integration-test-fi-analysis")
                        .build());

        // Test that workflow can be started without errors
        assertDoesNotThrow(() -> {
            WorkflowClient.start(workflow::startAnalysis, "test-fi-123");
        });
    }
}
