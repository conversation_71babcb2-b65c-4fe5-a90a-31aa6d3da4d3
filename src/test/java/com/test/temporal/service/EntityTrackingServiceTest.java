package com.test.temporal.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class EntityTrackingServiceTest {

    @Autowired
    private EntityTrackingService entityTrackingService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String TEST_FI_ID = "TEST-FI-123";
    private static final String TEST_ENTITY_ID = "1234567890123";

    @BeforeEach
    void setUp() {
        // Clean up any existing test data
        redisTemplate.delete("fi:" + TEST_FI_ID + ":entity:" + TEST_ENTITY_ID);
    }

    @Test
    public void testClaimEntityProcessing_FirstTime_ShouldReturnTrue() {
        // When claiming processing for the first time
        boolean claimed = entityTrackingService.claimEntityProcessing(TEST_FI_ID, TEST_ENTITY_ID);
        
        // Then it should be successful
        assertTrue(claimed);
        
        // And the entity should be marked as processed
        assertTrue(entityTrackingService.isEntityProcessed(TEST_FI_ID, TEST_ENTITY_ID));
    }

    @Test
    public void testClaimEntityProcessing_SecondTime_ShouldReturnFalse() {
        // Given an entity is already being processed
        entityTrackingService.claimEntityProcessing(TEST_FI_ID, TEST_ENTITY_ID);
        
        // When trying to claim processing again
        boolean claimed = entityTrackingService.claimEntityProcessing(TEST_FI_ID, TEST_ENTITY_ID);
        
        // Then it should fail
        assertFalse(claimed);
    }

    @Test
    public void testIsEntityProcessed_NotProcessed_ShouldReturnFalse() {
        // When checking an entity that hasn't been processed
        boolean processed = entityTrackingService.isEntityProcessed(TEST_FI_ID, TEST_ENTITY_ID);
        
        // Then it should return false
        assertFalse(processed);
    }

    @Test
    public void testIsEntityProcessed_AlreadyProcessed_ShouldReturnTrue() {
        // Given an entity is already being processed
        entityTrackingService.claimEntityProcessing(TEST_FI_ID, TEST_ENTITY_ID);
        
        // When checking if it's processed
        boolean processed = entityTrackingService.isEntityProcessed(TEST_FI_ID, TEST_ENTITY_ID);
        
        // Then it should return true
        assertTrue(processed);
    }

    @Test
    public void testRemoveEntityProcessing_ExistingEntity_ShouldReturnTrue() {
        // Given an entity is being processed
        entityTrackingService.claimEntityProcessing(TEST_FI_ID, TEST_ENTITY_ID);
        
        // When removing the processing mark
        boolean removed = entityTrackingService.removeEntityProcessing(TEST_FI_ID, TEST_ENTITY_ID);
        
        // Then it should be successful
        assertTrue(removed);
        
        // And the entity should no longer be marked as processed
        assertFalse(entityTrackingService.isEntityProcessed(TEST_FI_ID, TEST_ENTITY_ID));
    }

    @Test
    public void testRemoveEntityProcessing_NonExistentEntity_ShouldReturnFalse() {
        // When removing processing mark for an entity that wasn't processed
        boolean removed = entityTrackingService.removeEntityProcessing(TEST_FI_ID, TEST_ENTITY_ID);
        
        // Then it should return false
        assertFalse(removed);
    }

    @Test
    public void testDifferentFiIds_ShouldBeIndependent() {
        String fiId1 = "FI-001";
        String fiId2 = "FI-002";
        String entityId = "1234567890123";
        
        // When claiming processing for the same entity in different FI contexts
        boolean claimed1 = entityTrackingService.claimEntityProcessing(fiId1, entityId);
        boolean claimed2 = entityTrackingService.claimEntityProcessing(fiId2, entityId);
        
        // Then both should be successful (different FI contexts)
        assertTrue(claimed1);
        assertTrue(claimed2);
        
        // Clean up
        entityTrackingService.removeEntityProcessing(fiId1, entityId);
        entityTrackingService.removeEntityProcessing(fiId2, entityId);
    }
}
