package com.test.temporal.model;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class ModelTest {

    @Test
    public void testAnalysisConfig() {
        // Test no-args constructor
        AnalysisConfig config1 = new AnalysisConfig();
        assertNotNull(config1);
        assertEquals(0, config1.getMaxDepth());

        // Test all-args constructor
        AnalysisConfig config2 = new AnalysisConfig(5);
        assertEquals(5, config2.getMaxDepth());

        // Test setter
        config1.setMaxDepth(3);
        assertEquals(3, config1.getMaxDepth());

        // Test toString
        assertNotNull(config1.toString());
        assertTrue(config1.toString().contains("maxDepth=3"));

        // Test equals and hashCode
        AnalysisConfig config3 = new AnalysisConfig(3);
        assertEquals(config1, config3);
        assertEquals(config1.hashCode(), config3.hashCode());
    }

    @Test
    public void testNewEntity() {
        // Test no-args constructor
        NewEntity entity1 = new NewEntity();
        assertNotNull(entity1);

        // Test all-args constructor
        NewEntity entity2 = new NewEntity("123456789", "CNP-P");
        assertEquals("123456789", entity2.getId());
        assertEquals("CNP-P", entity2.getType());

        // Test setters
        entity1.setId("987654321");
        entity1.setType("CUI");
        assertEquals("987654321", entity1.getId());
        assertEquals("CUI", entity1.getType());

        // Test toString
        assertNotNull(entity2.toString());
        assertTrue(entity2.toString().contains("123456789"));
        assertTrue(entity2.toString().contains("CNP-P"));

        // Test equals and hashCode
        NewEntity entity3 = new NewEntity("123456789", "CNP-P");
        assertEquals(entity2, entity3);
        assertEquals(entity2.hashCode(), entity3.hashCode());
    }

    @Test
    public void testInitialEntities() {
        // Test no-args constructor
        InitialEntities entities1 = new InitialEntities();
        assertNotNull(entities1);
        assertNotNull(entities1.getCnps());
        assertNotNull(entities1.getCuis());
        assertTrue(entities1.getCnps().isEmpty());
        assertTrue(entities1.getCuis().isEmpty());

        // Test constructor with parameters
        InitialEntities entities2 = new InitialEntities(
                Arrays.asList("123", "456"),
                Arrays.asList("789", "012")
        );
        assertEquals(2, entities2.getCnps().size());
        assertEquals(2, entities2.getCuis().size());
        assertTrue(entities2.getCnps().contains("123"));
        assertTrue(entities2.getCuis().contains("789"));

        // Test setters with null safety
        entities1.setCnps(null);
        entities1.setCuis(null);
        assertNotNull(entities1.getCnps());
        assertNotNull(entities1.getCuis());
    }

    @Test
    public void testEntityData() {
        // Test no-args constructor
        EntityData data1 = new EntityData();
        assertNotNull(data1);
        assertNotNull(data1.getProperties());
        assertNotNull(data1.getDiscoveredEntities());

        // Test constructor with source
        EntityData data2 = new EntityData("ONRC");
        assertEquals("ONRC", data2.getSource());

        // Test constructor with all parameters
        Map<String, Object> props = new HashMap<>();
        props.put("test", "value");
        EntityData data3 = new EntityData("DEPABD", props, Arrays.asList(new NewEntity("123", "CNP-P")));
        assertEquals("DEPABD", data3.getSource());
        assertEquals(1, data3.getProperties().size());
        assertEquals(1, data3.getDiscoveredEntities().size());

        // Test addProperty
        data1.addProperty("key", "value");
        assertEquals("value", data1.getProperties().get("key"));

        // Test addDiscoveredEntity
        NewEntity entity = new NewEntity("456", "CUI");
        data1.addDiscoveredEntity(entity);
        assertTrue(data1.getDiscoveredEntities().contains(entity));

        // Test null safety
        data1.addDiscoveredEntity(null);
        data1.setProperties(null);
        data1.setDiscoveredEntities(null);
        assertNotNull(data1.getProperties());
        assertNotNull(data1.getDiscoveredEntities());
    }
}
