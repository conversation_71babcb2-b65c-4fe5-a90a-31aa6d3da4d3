package com.test.temporal;

import com.test.temporal.activity.ExtractionActivities;
import com.test.temporal.activity.ExternalDataActivities;
import com.test.temporal.activity.PersistenceActivities;
import com.test.temporal.model.AnalysisConfig;
import com.test.temporal.model.EntityData;
import com.test.temporal.model.InitialEntities;
import com.test.temporal.workflow.FiAnalysisWorkflow;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import io.temporal.testing.TestWorkflowEnvironment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Primary;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = FiAnalysisWorkflowTest.TestConfiguration.class)
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DirtiesContext
public class FiAnalysisWorkflowTest {

    @Autowired
    ConfigurableApplicationContext applicationContext;

    @Autowired
    TestWorkflowEnvironment testWorkflowEnvironment;

    @Autowired
    WorkflowClient workflowClient;

    @BeforeEach
    void setUp() {
        applicationContext.start();
    }

    @Test
    public void testFiAnalysisWorkflow() {
        FiAnalysisWorkflow workflow = workflowClient.newWorkflowStub(
                FiAnalysisWorkflow.class,
                WorkflowOptions.newBuilder()
                        .setTaskQueue("test-task-queue")
                        .setWorkflowId("test-fi-analysis")
                        .build());

        // Test the workflow execution
        assertDoesNotThrow(() -> workflow.startAnalysis("test-fi-123"));
    }

    @ComponentScan
    public static class TestConfiguration {

        @Bean
        @Primary
        public ExtractionActivities mockExtractionActivities() {
            ExtractionActivities mock = mock(ExtractionActivities.class);

            when(mock.getAnalysisConfig()).thenReturn(new AnalysisConfig(2));
            when(mock.extractInitialEntities(anyString())).thenReturn(
                    new InitialEntities(
                            Arrays.asList("1234567890123"),
                            Arrays.asList("12345678")
                    )
            );

            return mock;
        }

        @Bean
        @Primary
        public ExternalDataActivities mockExternalDataActivities() {
            ExternalDataActivities mock = mock(ExternalDataActivities.class);

            // Mock person data
            EntityData personData = new EntityData("DEPABD");
            Map<String, Object> personProps = new HashMap<>();
            personProps.put("nume", "Test");
            personData.setProperties(personProps);
            when(mock.fetchPersonBaseData(anyString())).thenReturn(personData);
            when(mock.fetchPersonRelatives(anyString())).thenReturn(new EntityData("DEPABD_REL"));
            when(mock.fetchRevisalData(anyString())).thenReturn(new EntityData("REVISAL"));
            when(mock.fetchPatrimvenData(anyString())).thenReturn(new EntityData("PATRIMVEN"));

            // Mock company data
            EntityData companyData = new EntityData("ONRC");
            Map<String, Object> companyProps = new HashMap<>();
            companyProps.put("denumire", "Test SRL");
            companyData.setProperties(companyProps);
            when(mock.fetchCompanyBaseData(anyString())).thenReturn(companyData);
            when(mock.fetchAffiliatedCompanies(anyString())).thenReturn(new EntityData("ONRC_AFF"));

            return mock;
        }

        @Bean
        @Primary
        public PersistenceActivities mockPersistenceActivities() {
            return mock(PersistenceActivities.class);
        }
    }
}
