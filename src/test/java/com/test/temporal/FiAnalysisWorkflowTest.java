package com.test.temporal;

import com.test.temporal.workflow.FiAnalysisWorkflow;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import io.temporal.testing.TestWorkflowEnvironment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;

import static com.test.temporal.Constants.TASK_QUEUE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(classes = FiAnalysisWorkflowTest.Configuration.class)
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DirtiesContext
public class FiAnalysisWorkflowTest {

    @Autowired
    ConfigurableApplicationContext applicationContext;

    @Autowired
    TestWorkflowEnvironment testWorkflowEnvironment;

    @Autowired
    WorkflowClient workflowClient;

    @BeforeEach
    void setUp() {
        applicationContext.start();
    }

    @Test
    public void testFiAnalysisWorkflow() {
		FiAnalysisWorkflow workflow = workflowClient.newWorkflowStub(
				FiAnalysisWorkflow.class,
				WorkflowOptions.newBuilder()
						.setTaskQueue(TASK_QUEUE)
						.setWorkflowId("integration-test-fi-analysis")
						.build());

		var res = workflow.startAnalysis("test-fi-123");
		assertNotNull(res);
		assertEquals("Analysis completed for FI ID: test-fi-123", res);
    }

    @ComponentScan
    public static class Configuration {}
}
