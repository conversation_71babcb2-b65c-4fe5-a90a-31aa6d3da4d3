package com.test.temporal;

import com.test.temporal.activity.ExtractionActivities;
import com.test.temporal.activity.ExternalDataActivities;
import com.test.temporal.activity.PersistenceActivities;
import com.test.temporal.model.AnalysisConfig;
import com.test.temporal.model.EntityData;
import com.test.temporal.model.InitialEntities;
import com.test.temporal.model.NewEntity;
import com.test.temporal.workflow.EntityExplorationWorkflow;
import com.test.temporal.workflow.EntityExplorationWorkflowImpl;
import com.test.temporal.workflow.FiAnalysisWorkflow;
import com.test.temporal.workflow.FiAnalysisWorkflowImpl;
import io.temporal.testing.TestWorkflowRule;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class FiAnalysisWorkflowTest {

    @RegisterExtension
    public static final TestWorkflowRule testWorkflowRule =
            TestWorkflowRule.newBuilder()
                    .setWorkflowTypes(FiAnalysisWorkflowImpl.class, EntityExplorationWorkflowImpl.class)
                    .setActivityImplementations(
                            createMockExtractionActivities(),
                            createMockExternalDataActivities(),
                            createMockPersistenceActivities()
                    )
                    .build();

    @Test
    public void testFiAnalysisWorkflow() {
        FiAnalysisWorkflow workflow = testWorkflowRule.getWorkflowClient()
                .newWorkflowStub(FiAnalysisWorkflow.class);

        // Test the workflow execution
        workflow.startAnalysis("test-fi-123");

        // The workflow should complete without errors
        // In a real test, you would verify the expected behavior
    }

    private static ExtractionActivities createMockExtractionActivities() {
        ExtractionActivities mock = mock(ExtractionActivities.class);
        
        when(mock.getAnalysisConfig()).thenReturn(new AnalysisConfig(2));
        when(mock.extractInitialEntities(anyString())).thenReturn(
                new InitialEntities(
                        Arrays.asList("1234567890123"),
                        Arrays.asList("12345678")
                )
        );
        
        return mock;
    }

    private static ExternalDataActivities createMockExternalDataActivities() {
        ExternalDataActivities mock = mock(ExternalDataActivities.class);
        
        // Mock person data
        EntityData personData = new EntityData("DEPABD");
        Map<String, Object> personProps = new HashMap<>();
        personProps.put("nume", "Test");
        personData.setProperties(personProps);
        when(mock.fetchPersonBaseData(anyString())).thenReturn(personData);
        when(mock.fetchPersonRelatives(anyString())).thenReturn(new EntityData("DEPABD_REL"));
        when(mock.fetchRevisalData(anyString())).thenReturn(new EntityData("REVISAL"));
        when(mock.fetchPatrimvenData(anyString())).thenReturn(new EntityData("PATRIMVEN"));
        
        // Mock company data
        EntityData companyData = new EntityData("ONRC");
        Map<String, Object> companyProps = new HashMap<>();
        companyProps.put("denumire", "Test SRL");
        companyData.setProperties(companyProps);
        when(mock.fetchCompanyBaseData(anyString())).thenReturn(companyData);
        when(mock.fetchAffiliatedCompanies(anyString())).thenReturn(new EntityData("ONRC_AFF"));
        
        return mock;
    }

    private static PersistenceActivities createMockPersistenceActivities() {
        return mock(PersistenceActivities.class);
    }
}
