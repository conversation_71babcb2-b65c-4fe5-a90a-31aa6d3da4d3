version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: demo-temporal-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  temporal:
    image: temporalio/auto-setup:1.22.0
    container_name: demo-temporal-server
    ports:
      - "7233:7233"
      - "8233:8233"
    environment:
      - DB=postgresql
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=temporal
      - POSTGRES_SEEDS=postgres
      - DYNAMIC_CONFIG_FILE_PATH=config/dynamicconfig/development-sql.yaml
    volumes:
      - temporal_data:/etc/temporal/config/dynamicconfig
    depends_on:
      - postgres
    restart: unless-stopped

  postgres:
    image: postgres:13
    container_name: demo-temporal-postgres
    environment:
      POSTGRES_DB: temporal
      POSTGRES_USER: temporal
      POSTGRES_PASSWORD: temporal
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  temporal-ui:
    image: temporalio/ui:2.21.3
    container_name: demo-temporal-ui
    ports:
      - "8080:8080"
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:3000
    depends_on:
      - temporal
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
  temporal_data:
    driver: local
  postgres_data:
    driver: local

networks:
  default:
    name: demo-temporal-network
