# FI Analysis Temporal Workflow

This project implements a robust orchestration system for analyzing Integrity Forms (Formulare de Integritate) using Temporal workflows. The system manages dependencies between external calls and ensures resilience through design.

## Key Features

- **Single Common Depth**: Uses one `maxDepth` configuration for all entity types (CNP and CUI)
- **Parallel and Sequential Processing**: Optimizes external API calls with intelligent dependency management
- **Resilient Design**: Built on Temporal for fault tolerance and reliability
- **Lombok Integration**: Uses Lombok annotations for cleaner code

## Architecture

### Data Models

- **AnalysisConfig**: Contains configuration with a single `maxDepth` for all entities
- **InitialEntities**: Lists of CNPs and CUIs extracted from the Integrity Form
- **NewEntity**: Represents newly discovered entities during analysis
- **EntityData**: Generic container for data returned by external activities

### Workflows

1. **FiAnalysisWorkflow** (Main Orchestrator)
   - Extracts initial entities from the Integrity Form
   - Launches child workflows for each entity
   - Tracks processed entities to avoid duplicates
   - Coordinates the overall analysis process

2. **EntityExplorationWorkflow** (Child Explorer)
   - Explores individual entities (persons or companies)
   - Manages parallel/sequential external API calls
   - Discovers new related entities
   - Recursively launches exploration for discovered entities

### Activities

- **ExtractionActivities**: Gets configuration and extracts initial entities
- **ExternalDataActivities**: Handles all external API calls (DEPABD, ONRC, REVISAL, etc.)
- **PersistenceActivities**: Saves collected data to the database

## Processing Flow

### For Persons (CNP-P, CNP-R):
1. **Parallel calls**: Base data, REVISAL data, PATRIMVEN data
2. **Sequential call**: Relatives data (depends on base data)
3. **Discovery**: New entities from all sources
4. **Recursion**: Launch exploration for discovered entities

### For Companies (CUI):
1. **Sequential calls**: Base data, then affiliated companies
2. **Discovery**: New entities (administrators, affiliated companies)
3. **Recursion**: Launch exploration for discovered entities

## Configuration

The system uses a single `maxDepth` parameter that applies to all entity types:

```java
AnalysisConfig config = new AnalysisConfig(3); // 3 levels deep for all entities
```

## Usage

### Start Analysis via REST API

```bash
# Start analysis for a specific FI ID
GET /start/{fiId}

# Start analysis with default FI ID
GET /start
```

### Example

```bash
curl http://localhost:8080/start/FI-2024-001
```

## Testing

Run the unit tests to verify the workflow implementation:

```bash
mvn test
```

## Dependencies

- Spring Boot 3.5.5
- Temporal Spring Boot Starter 1.30.1
- Lombok (for cleaner POJOs)
- JUnit 5 (for testing)

## Key Changes from Original

- **Unified Depth**: Removed separate `maxDepthCnp` and `maxDepthCui`, using single `maxDepth`
- **Lombok Integration**: All model classes use Lombok annotations (@Data, @NoArgsConstructor, @AllArgsConstructor)
- **Simplified Configuration**: Single depth parameter simplifies configuration management
- **Consistent Processing**: Same depth logic applies to all entity types

## Running the Application

```bash
mvn spring-boot:run
```

The application will start on port 8080 and be ready to process FI analysis requests.
